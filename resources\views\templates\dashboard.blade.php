<!DOCTYPE html>
<html lang="en">


<!-- Mirrored from demo.adminkit.io/ by HTTrack Website Copier/3.x [XR&CO'2014], Tue, 21 Mar 2023 07:40:34 GMT -->
<!-- Added by HTTrack -->
<meta http-equiv="content-type" content="text/html;charset=UTF-8" /><!-- /Added by HTTrack -->

<head>
    {{-- @php
    $settings = App\Models\settings::first();
    @endphp --}}
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description"
        content="SimPKL Skansawa &amp; Sistem Informasi Praktik Kerja <PERSON> SMK Negeri 1 Wadaslintang">
    <meta name="author" content="SkansawaCreative">
    <meta name="keywords"
        content="simpkl, skansawa, smk negeri 1 wadaslintang, wadaslintang, bootstrap, bootstrap 5, admin, dashboard, template, responsive, css, sass, html, theme, front-end, ui kit, web">

    <link rel="preconnect" href="https://fonts.gstatic.com/">
    {{--
    <link rel="shortcut icon" href="{{ url('storage/'.$settings->logo) }}" />
    <link rel="apple-touch-icon-precomposed" href="{{ url('storage/'.$settings->logo) }}" /> --}}

    <link rel="canonical" href="{{ url('/') }}" />

    <title>{{ $title }}</title>

    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600&amp;display=swap" rel="stylesheet">

    <!-- Choose your prefered color scheme -->
    <!-- <link href="css/light.css" rel="stylesheet"> -->
    <!-- <link href="css/dark.css" rel="stylesheet"> -->

    <!-- BEGIN SETTINGS -->
    <!-- Remove this after purchasing -->
    <link class="js-stylesheet" href="{{ url('/adminkit/css/light.css') }}" rel="stylesheet">

    {{--
    <link rel="stylesheet" href="{{ url('https://unpkg.com/leaflet@1.8.0/dist/leaflet.css') }}"
        integrity="sha512-hoalWLoI8r4UszCkZ5kL8vayOGVae1oxXe/2A4AO6J9+580uKHDO3JdHb7NzwwzK5xr/Fs0W40kiNHxM9vyTtQ=="
        crossorigin="" />
    <script src="{{ url('https://unpkg.com/leaflet@1.8.0/dist/leaflet.js') }}"
        integrity="sha512-BB3hKbKWOc9Ez/TAwyWxNXeoV9c1v6FIeYiBieIWkpLjauysF18NzgR1MBNBXf8/KABdlkX68nAhlwcDFLGPCQ=="
        crossorigin=""></script> --}}

    <script src="{{ url('/adminkit/js/settings.js') }}"></script>
    <style>
        body {
            opacity: 0;
        }

        .choices__list--dropdown,
        .choices__list[aria-expanded] {
            width: max-content;
        }

        .choices__list--dropdown .choices__item--choice.is-highlighted {
            background-color: #e9ecef;
        }

        .choices[data-type*=select-one]>.choices__list>.choices__list>.choices__item--selectable {
            padding-right: 8px;
        }

        .choices[data-type*=select-one]>.choices__list>.choices__list>.choices__item--selectable::after {
            display: none;
        }

        .btn-custom-xs {
            padding: 0.25rem 0.4rem;
            /* Adjust padding as needed */
            font-size: 0.7rem;
            /* Adjust font size as needed */
            line-height: 0.7;
            /* Adjust line-height as needed */
            border-radius: 0.2rem;
            /* Adjust border-radius as needed */
        }

        .swal2-popup {
            font-size: 10pt !important;
            /* Adjust the value as needed */
        }

        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.9);
            /* Semi-transparent white overlay */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            /* Ensure it's on top of other content */
            transition: opacity 0.5s ease-out;
            /* Fade-out transition */
        }

        .page-loader.fade-out {
            opacity: 0;
            pointer-events: none;
            /* Disable interaction with the faded-out loader */
        }

        .badge-xs {
            font-size: 0.7em;
            /* Adjust as needed */
            padding: 0.2em 0.4em;
            /* Adjust as needed */
        }

        /* Custom pagination styling */
        .pagination-sm .page-link {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.375rem;
        }

        .pagination .page-item.active .page-link {
            background-color: #3b7ddd;
            border-color: #3b7ddd;
            color: #fff;
        }

        .pagination .page-link {
            color: #6c757d;
            border: 1px solid #dee2e6;
            transition: all 0.15s ease-in-out;
        }

        .pagination .page-link:hover {
            color: #3b7ddd;
            background-color: #f8f9fa;
            border-color: #dee2e6;
        }

        .pagination .page-item.disabled .page-link {
            color: #adb5bd;
            background-color: #fff;
            border-color: #dee2e6;
        }

        .card-footer {
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 1rem 1.25rem;
        }

        /* .ql-editor {
            height: 100px;
            overflow-y: auto;
        } */
    </style>
    <!-- END SETTINGS -->
    {{-- <script async src="https://www.googletagmanager.com/gtag/js?id=UA-120946860-10"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'UA-120946860-10', { 'anonymize_ip': true });
    </script> --}}
</head>
<!--
  HOW TO USE:
  data-theme: default (default), dark, light, colored
  data-layout: fluid (default), boxed
  data-sidebar-position: left (default), right
  data-sidebar-layout: default (default), compact
-->

<body data-theme="default" data-layout="fluid" data-sidebar-position="left" data-sidebar-layout="compact">
    <div class="wrapper">
        <nav id="sidebar" class="sidebar js-sidebar">
            <div class="sidebar-content js-simplebar">
                <a class="sidebar-brand" href="{{ url('/dashboard') }}">
                    <span class="sidebar-brand-text align-middle">
                        {{-- {{$settings->first_name}}
                        <sup><small class="badge bg-primary text-uppercase">{{$settings->last_name}}</small></sup> --}}
                    </span>
                    {{-- <img class="sidebar-brand-icon align-middle p-0" src="{{ url('storage/'.$settings->logo) }}"
                        width="32px" height="32px"> --}}
                    {{-- <svg class="sidebar-brand-icon align-middle" width="32px" height="32px" viewBox="0 0 24 24"
                        fill="none" stroke="#FFFFFF" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="miter"
                        color="#FFFFFF" style="margin-left: -3px">
                        <path d="M12 4L20 8.00004L12 12L4 8.00004L12 4Z"></path>
                        <path d="M20 12L12 16L4 12"></path>
                        <path d="M20 16L12 20L4 16"></path>
                    </svg> --}}
                </a>

                <div class="sidebar-user">
                    <div class="d-flex justify-content-center">
                        <div class="flex-shrink-0">
                            {{-- @if (auth()->user()->foto_karyawan)
                            <img src="{{ url('/storage/'.auth()->user()->foto_karyawan) }}"
                                class="avatar img-fluid rounded me-1" alt="{{auth()->user()->name}}" />
                            @else
                            <img src="{{ url('assets/img/foto_default.jpg') }}" class="avatar img-fluid rounded me-1"
                                alt="{{auth()->user()->name}}" />
                            @endif --}}
                        </div>
                        <div class="flex-grow-1 ps-2">
                            <a class="sidebar-user-title dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                {{-- {{ Str::title(strtolower(auth()->user()->name)) }} --}}
                            </a>
                            <div class="dropdown-menu dropdown-menu-start">
                                <a class="dropdown-item" href="{{ url('/my-profile') }}"><i class="align-middle me-1"
                                        data-feather="user"></i> Profile</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url('/my-profile/edit-password') }}"><i
                                        class="align-middle me-1" data-feather="settings"></i> Ubah Password</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url('/logout') }}">Log out</a>
                            </div>

                            <div class="sidebar-user-subtitle">
                                {{-- {{ auth()->user()->Jabatan->nama_jabatan }} --}}
                            </div>
                        </div>
                    </div>
                </div>

                <ul class="sidebar-nav">
                    <li class="sidebar-item {{ Request::is('dashboard*') ? 'active' : '' }}">
                        <a href="{{ url('/dashboard') }}" class="sidebar-link">
                            <i class="align-middle" data-feather="home"></i> <span class="align-middle">Dashboard</span>
                        </a>
                    </li>

                    <li class="sidebar-item {{ Request::is('siswa*') ? 'active' : '' }}">
                        <a href="{{ url('/siswa') }}" class="sidebar-link">
                            <i class="align-middle" data-feather="users"></i> <span class="align-middle">Siswa</span>
                        </a>
                    </li>

                    {{-- <li class="sidebar-item {{ Request::is('notifications*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/notifications') }}">
                            <i class="align-middle" data-feather="bell"></i> <span
                                class="align-middle">Notifications</span>
                            <span class="sidebar-badge badge bg-primary fw-normal">
                                {{ auth()->user()->notifications()->whereNull('read_at')->count() }}
                            </span>
                        </a>
                    </li> --}}

                    {{-- <li class="sidebar-item {{ Request::is('my-profile*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/my-profile') }}">
                            <i class="align-middle" data-feather="user"></i> <span class="align-middle">Profile</span>
                        </a>
                    </li> --}}
                    <li class="sidebar-item {{ Request::is('data-cuti*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/data-cuti') }}">
                            <i class="align-middle" data-feather="shuffle"></i>
                            <span class="align-middle">Permintaan Izin</span>
                            <span class="sidebar-badge badge bg-primary fw-normal">
                                {{-- {{ DB::table('cutis')->where('status_cuti','Pending')->count('id') }} --}}
                            </span>
                        </a>
                    </li>

                    <li class="sidebar-item {{ Request::is('pengajuan-absensi*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/pengajuan-absensi') }}">
                            <i class="align-middle" data-feather="repeat"></i>
                            <span class="align-middle">Pengajuan Absensi</span>
                            <span class="sidebar-badge badge bg-primary fw-normal">
                                {{-- {{ DB::table('mapping_shifts')->where('status_pengajuan', 'Menunggu')->count('id')
                                }} --}}
                            </span>
                        </a>
                    </li>

                    <li class="sidebar-item {{ Request::is('pengumuman*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/pengumuman') }}">
                            <i class="align-middle" data-feather="message-circle"></i> <span
                                class="align-middle">Pengumuman</span>
                        </a>
                    </li>

                    <li
                        class="sidebar-item {{ Request::is('admin*') || Request::is('pembimbing*') || Request::is('jurusan*') || Request::is('jabatan*') || Request::is('lokasi-kantor*') || Request::is('pegawai*') ? 'active' : '' }}">
                        <a data-bs-target="#data" data-bs-toggle="collapse" class="sidebar-link collapsed">
                            <i class="align-middle" data-feather="database"></i> <span class="align-middle">Data</span>
                        </a>
                        <ul id="data"
                            class="sidebar-dropdown list-unstyled collapse {{ Request::is('admin*') || Request::is('pembimbing*') || Request::is('jurusan*') || Request::is('jabatan*') || Request::is('lokasi-kantor*') || Request::is('pegawai*') ? 'show' : '' }}"
                            data-bs-parent="#sidebar">
                            <li class="sidebar-item {{ Request::is('admin*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/admin') }}">Administrator</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('pembimbing*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/pembimbing') }}">Pembimbing</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('jurusan*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/jurusan') }}">Jurusan</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('jabatan*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/jabatan') }}">Kelas</a>
                            </li>
                            <li class="sidebar-item">
                                <a class="sidebar-link {{ Request::is('lokasi-kantor*') ? 'active' : '' }}"
                                    href="{{ url('/lokasi-kantor') }}">Lokasi PKL</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('pegawai*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/pegawai') }}">Siswa</a>
                            </li>
                        </ul>
                    </li>

                    <li
                        class="sidebar-item {{ Request::is('jadwal*') || Request::is('survey-shift*') ? 'active' : '' }}">
                        <a data-bs-target="#jadwal" data-bs-toggle="collapse" class="sidebar-link collapsed">
                            <i class="align-middle" data-feather="clock"></i> <span class="align-middle">Jadwal</span>
                        </a>
                        <ul id="jadwal"
                            class="sidebar-dropdown list-unstyled collapse {{ Request::is('jadwal*') || Request::is('survey-shift*') ? 'show' : '' }}"
                            data-bs-parent="#sidebar">
                            <li class="sidebar-item {{ Request::is('jadwal*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/jadwal') }}">Jadwal</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('survey-shift*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/survey-shift/data') }}">Pemetaan</a>
                            </li>
                        </ul>
                    </li>

                    <li
                        class="sidebar-item {{ Request::is('dudi*') || Request::is('survey-lokasi*') ? 'active' : '' }}">
                        <a data-bs-target="#dudi" data-bs-toggle="collapse" class="sidebar-link collapsed">
                            <i class="align-middle" data-feather="server"></i> <span class="align-middle">Dudi</span>
                        </a>
                        <ul id="dudi"
                            class="sidebar-dropdown list-unstyled collapse {{ Request::is('dudi*') || Request::is('survey-lokasi*') ? 'show' : '' }}"
                            data-bs-parent="#sidebar">
                            <li class="sidebar-item {{ Request::is('dudi*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/dudi') }}">Dudi</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('survey-lokasi*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/survey-lokasi/data') }}">Pemetaan</a>
                            </li>
                        </ul>
                    </li>

                    <li
                        class="sidebar-item {{ Request::is('data-absen*') || Request::is('rekap-data*') ? 'active' : '' }}">
                        <a data-bs-target="#presensi" data-bs-toggle="collapse" class="sidebar-link collapsed">
                            <i class="align-middle" data-feather="book-open"></i> <span
                                class="align-middle">Presensi</span>
                        </a>
                        <ul id="presensi"
                            class="sidebar-dropdown list-unstyled collapse {{ Request::is('data-absen*') || Request::is('rekap-data*') ? 'show' : '' }}"
                            data-bs-parent="#sidebar">
                            <li class="sidebar-item {{ Request::is('data-absen*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/data-absen') }}">Data Presensi</a>
                            </li>
                            <li class="sidebar-item {{ Request::is('rekap-data*') ? 'active' : '' }}">
                                <a class="sidebar-link" href="{{ url('/rekap-data') }}">Rekap Data</a>
                            </li>
                        </ul>
                    </li>

                    <li class="sidebar-item {{ Request::is('settings*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/settings') }}">
                            <i class="align-middle" data-feather="settings"></i> <span
                                class="align-middle">Settings</span>
                        </a>
                    </li>

                    <li class="sidebar-item {{ Request::is('tools*') ? 'active' : '' }}">
                        <a class="sidebar-link" href="{{ url('/tools') }}">
                            <i class="align-middle" data-feather="sliders"></i> <span class="align-middle">Tools</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <div id="page-loader" class="page-loader d-flex justify-content-center align-items-center vh-100 bg-light">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>

        <div class="main" id="main-content">
            <nav class="navbar navbar-expand navbar-light navbar-bg">
                <a class="sidebar-toggle js-sidebar-toggle">
                    <i class="hamburger align-self-center"></i>
                </a>

                <form class="d-none d-sm-inline-block">
                    <div class="input-group input-group-navbar">
                        <input type="text" class="form-control" placeholder="Search…" aria-label="Search">
                        <button class="btn" type="button">
                            <i class="align-middle" data-feather="search"></i>
                        </button>
                    </div>
                </form>

                <div class="navbar-collapse collapse">
                    <ul class="navbar-nav navbar-align">
                        <li class="nav-item dropdown">
                            {{-- <a class="nav-icon dropdown-toggle" href="{{ url('/notifications') }}"
                                id="alertsDropdown">
                                <div class="position-relative">
                                    <i class="align-middle" data-feather="bell"></i>
                                    <span class="indicator">{{
                                        auth()->user()->notifications()->whereNull('read_at')->count() }}</span>
                                </div>
                            </a> --}}
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-icon dropdown-toggle" href="{{ url('/data-cuti') }}" id="alertsDropdown">
                                <div class="position-relative">
                                    <i class="align-middle" data-feather="shuffle"></i>
                                    {{-- <span class="indicator">{{
                                        DB::table('cutis')->where('status_cuti','Pending')->count('id') }}</span> --}}
                                </div>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-icon dropdown-toggle" href="{{ url('/pengajuan-absensi') }}"
                                id="alertsDropdown">
                                <div class="position-relative">
                                    <i class="align-middle" data-feather="repeat"></i>
                                    {{-- <span class="indicator">{{
                                        DB::table('mapping_shifts')->where('status_pengajuan',
                                        'Menunggu')->count('id') }}</span> --}}
                                </div>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-icon js-fullscreen d-none d-lg-block" href="#">
                                <div class="position-relative">
                                    <i class="align-middle" data-feather="maximize"></i>
                                </div>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-icon pe-md-0 dropdown-toggle" href="#" data-bs-toggle="dropdown">
                                {{-- @if (auth()->user()->foto_karyawan)
                                <img src="{{ url('/storage/'.auth()->user()->foto_karyawan) }}"
                                    class="avatar img-fluid rounded" alt="{{auth()->user()->name}}" />
                                @else
                                <img src="{{ url('assets/img/foto_default.jpg') }}" class="avatar img-fluid rounded"
                                    alt="{{auth()->user()->name}}" />
                                @endif --}}
                            </a>
                            <div class="dropdown-menu dropdown-menu-end">
                                <a class="dropdown-item" href="{{ url('/my-profile') }}"><i class="align-middle me-1"
                                        data-feather="user"></i> Profile</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url('/my-profile/edit-password') }}"><i
                                        class="align-middle me-1" data-feather="settings"></i> Ubah Password</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ url('/logout') }}">Log out</a>
                            </div>
                        </li>
                    </ul>
                </div>
            </nav>

            {{-- aslinya tidak ada padding --}}
            <main class="content p-4">
                <div class="container-fluid p-0" style="font-size: 97%;">
                    @yield('isi')
                </div>
            </main>

            <footer class="footer">
                <div class="container-fluid">
                    <div class="row text-muted">
                        <div class="col-12 text-start">
                            <p class="mb-0">
                                <a href="https://sobocamp.site/" target="_blank"
                                    class="text-muted"><strong>Sobocamp</strong></a> x
                                <a href="https://smkn1wadaslintang.sch.id/" target="_blank"
                                    class="text-muted"><strong>SkansawaCreative</strong></a> &copy; 2025
                            </p>
                        </div>
                        {{-- <div class="col-6 text-end">
                            <ul class="list-inline">
                                <li class="list-inline-item">
                                    <a class="text-muted" href="#">Support</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-muted" href="#">Help Center</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-muted" href="#">Privacy</a>
                                </li>
                                <li class="list-inline-item">
                                    <a class="text-muted" href="#">Terms</a>
                                </li>
                            </ul>
                        </div> --}}
                    </div>
                </div>
            </footer>
        </div>
    </div>
    <script src="{{ url('/adminkit/js/app.js') }}"></script>
    {{-- <script src="{{ url('/adminkit/js/datatables.js') }}"></script> --}}
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    {{-- <script src="{{ url('/push/bin/push.js') }}"></script> --}}
    {{-- <script src="{{ url('/js/app.js') }}"></script> --}}
    <script>
        // function cek_notif() {
        //     var type = "success";
		// 		var duration = 10000;
		// 		var ripple = false;
		// 		var dismissible = true;
		// 		var positionX = "right";
		// 		var positionY = "top";
		// 		var message = "Notifikasi baru";

        //     window.notyf.open({
		// 			type,
		// 			message,
		// 			duration,
		// 			ripple,
		// 			dismissible,
		// 			position: {
		// 				x: positionX,
		// 				y: positionY
		// 			}
		// 		});
        // }
        // cek_notif();


    </script>
    {{-- <script>
        function getLocation() {
          if (navigator.geolocation) {
              navigator.geolocation.getCurrentPosition(showPosition);
          } else {
              x.innerHTML = "Geolocation is not supported by this browser.";
          }
      }

      function showPosition(position) {
          $('#lat').val(position.coords.latitude);
          $('#lat2').val(position.coords.latitude);
          $('#long').val(position.coords.longitude);
          $('#long2').val(position.coords.longitude);
      }

      setInterval(getLocation, 1000);
    </script> --}}
    <script>
        $(function(){
          $('form').on('submit', function(){
              $(':input[type="submit"]').prop('disabled', true);
          })
      })
    </script>
    <script>
        config = {
          enableTime: true,
          noCalendar: true,
          dateFormat: "H:i",
          time_24hr: true,
      }

      flatpickr("input[type=datetime-local]", config)
      flatpickr("input[type=time]", config)
      flatpickr("input[type=datetime]", {})
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
			// Choices.js
            const elements = document.querySelectorAll('.choices-single');
            elements.forEach(element => {
                new Choices(element, {
                    // your options here
                    itemSelectText: '',
                });
            });
		});
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const pageLoader = document.getElementById('page-loader');
            const mainContent = document.getElementById('main-content');

            // Add a delay (optional) to ensure all resources are loaded or for visual effect
            setTimeout(() => {
                pageLoader.classList.add('fade-out'); // Start the fade-out animation
                mainContent.style.opacity = 1; // Make main content visible

                // Remove the loader from the DOM after the animation completes
                pageLoader.addEventListener('transitionend', () => {
                pageLoader.remove();
                }, { once: true }); // Ensure the event listener is removed after one use
            }, 150); // Adjust delay as needed (e.g., 500ms)
        });
    </script>

    {{-- <script>
        document.addEventListener("DOMContentLoaded", function() {
			$("#datatables").DataTable({
				// responsive: true,
                pageLength: 25,
			});

            $("#datatables-responsive").DataTable({
				responsive: true,
                pageLength: 25
			});
		});
    </script> --}}

    @stack('script')
    @include('sweetalert::alert')
</body>

</html>
