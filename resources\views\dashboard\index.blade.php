@extends('templates.dashboard')
@section('isi')
<div class="row mb-2 mb-xl-3">
    <div class="col-auto d-none d-sm-block">
        <h3>{{ $title }}</h3>
    </div>
</div>
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <form action="{{ url('/dashboard') }}">
                    <div class="row">
                        <div class="col-lg-2 col-sm-4 col-md-3 col-xxl-2 mb-2 mb-sm-0">
                            <input type="datetime" class="form-control" name="mulai" placeholder="Mulai" id="mulai"
                                value="{{ request('mulai') }}">
                        </div>
                        <div class="col-lg-2 col-sm-4 col-md-3 col-xxl-2 mb-2 mb-sm-0">
                            <input type="datetime" class="form-control" name="akhir" placeholder="Sampai" id="akhir"
                                value="{{ request('akhir') }}">
                        </div>
                        <div class="col-lg-2 col-sm-4 col-md-2 col-xxl-2">
                            <button type="submit" id="search" class="btn btn-primary w-100">Tampilkan</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="table-responsive">
                <table class="table table-striped my-0">
                    <thead>
                        <tr>
                            <th>No.</th>
                            <th>Tanggal</th>
                            <th>Terjadwal</th>
                            <th>Masuk</th>
                            <th>Pulang</th>
                            <th>Libur</th>
                            <th>Sakit</th>
                            <th>Izin TM</th>
                            <th>Izin T</th>
                            <th>Izin PC</th>
                            <th>Pending</th>
                            <th>Alpha</th>
                        </tr>
                    </thead>
                    <tbody>
                        {{-- @php
                        $no = 1;
                        @endphp
                        @foreach($info_absen as $ia)
                        <tr>
                            <td width="50">{{ $no++ }}</td>
                            <td>{{ $ia->tanggal }}</td>
                            <td>{{ $ia->terjadwal }}</td>
                            <td class="text-success fw-bold">{{ $ia->masuk }}</td>
                            <td class="text-success fw-bold">{{ $ia->pulang }}</td>
                            <td>
                                <a class="text-info fw-bold text-decoration-none"
                                    href="{{ url('/absen-by-status?tanggal='.$ia->tanggal.'&status=Libur') }}">
                                    {{ $ia->libur }}
                                </a>
                            </td>
                            <td>
                                <a class="text-warning fw-bold text-decoration-none"
                                    href="{{ url('/absen-by-status?tanggal='.$ia->tanggal.'&status=Izin Sakit') }}">
                                    {{ $ia->izin_sakit }}
                                </a>
                            </td>
                            <td>
                                <a class="text-warning fw-bold text-decoration-none"
                                    href="{{ url('/absen-by-status?tanggal='.$ia->tanggal.'&status=Izin Tidak Masuk') }}">
                                    {{ $ia->izin_tidak_masuk }}
                                </a>
                            </td>
                            <td>
                                <a class="text-warning fw-bold text-decoration-none"
                                    href="{{ url('/absen-by-status?tanggal='.$ia->tanggal.'&status=Izin Telat') }}">
                                    {{ $ia->izin_telat }}
                                </a>
                            </td>
                            <td>
                                <a class="text-warning fw-bold text-decoration-none"
                                    href="{{ url('/absen-by-status?tanggal='.$ia->tanggal.'&status=Izin Pulang Cepat') }}">
                                    {{ $ia->izin_pulang_cepat }}
                                </a>
                            </td>
                            <td class="text-secondary fw-bold">
                                {{ $info_pengajuan[$ia->tanggal] ?? '-' }}</td>
                            <td>
                                <a class="text-danger fw-bold text-decoration-none"
                                    href="{{ url('/absen-by-status?tanggal='.$ia->tanggal.'&status=Tidak Masuk') }}">
                                    {{ $ia->alpha }}
                                </a>
                            </td>
                        </tr>
                        @endforeach --}}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{{-- @php
$mulai = request('mulai') ? request('mulai') : date('Y-m-d', strtotime('monday this week'));
$akhir = request('akhir') ? request('akhir') : date('Y-m-d');
@endphp

<div class="row">
    <div class="col-12 col-lg-5">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Presensi Siswa PKL Hari Ini</h5>
                <h6 class="card-subtitle text-muted">Data diambil tanggal <b>{{ date('d-m-Y') }}</b></h6>
            </div>
            <div class="card-body">
                <div class="chart w-100">
                    <div id="apexcharts-pie"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12 col-lg-7">
        <div class="card">
            <div class="card-header mb-0">
                <h5 class="card-title">Presensi Siswa PKL Berdasar Tanggal</h5>
                <h6 class="card-subtitle text-muted">Data diambil dari tanggal <b>{{ date('d-m-Y', strtotime($mulai))
                        }}</b>
                    sampai <b>{{ date('d-m-Y', strtotime($akhir)) }}</b></h6>
            </div>
            <div class="card-body mt-0">
                <div class="chart w-100">
                    <div id="apexcharts-area"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12 col-lg-5">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col mt-0">
                        <h5 class="card-title">Belum Mapping</h5>
                    </div>

                    <div class="col-auto">
                        <div class="stat text-primary">
                            <i class="align-middle" data-feather="users"></i>
                        </div>
                    </div>
                </div>
                <h1 class="mt-1 mb-3">{{ $jumlah_belum_mapping }}</h1>
                <div class="mb-0">
                    <a href="{{ url('/pegawai?pembimbing=null') }}">Lihat Data</a>
                </div>
            </div>
        </div>
    </div>
</div> --}}

@push('script')
{{-- <script>
    $(document).ready(function() {
        $('#mulai').change(function(){
            var mulai = $(this).val();
        $('#akhir').val(mulai);
        });
    });
</script> --}}

{{-- <script>
    document.addEventListener("DOMContentLoaded", function() {
        // Chart area
        var options = {
            chart: {
                height: 304,
                type: "line",
                zoom: {
                    enabled: false
                },
            },
            // plotOptions: {
            //     bar: {
            //         horizontal: false,
            //         endingShape: "rounded",
            //         columnWidth: "55%",
            //     },
            // },
            dataLabels: {
                enabled: false
            },
            stroke: {
                width: 2,
                curve: "smooth",
                // dashArray: [0, 8, 5]
            },
            series: [],
            noData: {
                text: 'Loading...'
            },
            colors: [],
            markers: {
                size: 0,
                style: "hollow", // full, hollow, inverted
            },
            xaxis: {
                categories: [],
            },
            tooltip: {},
            grid: {
                borderColor: "#f1f1f1",
            }
        }
        var chart = new ApexCharts(
            document.querySelector("#apexcharts-area"),
            options
        );
        chart.render();

        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        // Chart area
        $.ajax({
            datatype: 'json',
            url: "{{ url('/chart-data') }}",
            method: 'GET',
            data: {
                "mulai": "{{ request('mulai') }}",
                "akhir": "{{ request('akhir') }}"
            }
        }).done(async function(data) {
            chart.updateOptions({
                noData: {
                    text: ''
                },
                colors: ["#198754", "#66DA26", "#0d6efd", "#6f42c1", "#FF9800", "#dc3545"],
                series: [
                {
                    name: 'Masuk',
                    data: data.map(item => item.masuk)
                },
                {
                    name: 'Pulang',
                    data: data.map(item => item.pulang)
                },
                {
                    name: 'Libur',
                    data: data.map(item => item.libur)
                },
                {
                    name: 'Sakit',
                    data: data.map(item => item.izin_sakit)
                },
                {
                    name: 'Izin Tidak Masuk',
                    data: data.map(item => item.izin_tidak_masuk)
                },
                {
                    name: 'Alpha',
                    data: data.map(item => item.alpha)
                }
                ],
                xaxis: {
                    categories: data.map(item => item.tanggal_short)
                }
            })
        });

        // Chart pie
        var optionspie = {
            chart: {
                height: 350,
                type: "donut",
                zoom: {
                    enabled: false
                },
            },
            dataLabels: {
                enabled: false
            },
            legend: {
                position: 'bottom'
            },
            series: [],
            noData: {
                text: 'Loading...'
            },
        }
        var chartpie = new ApexCharts(
            document.querySelector("#apexcharts-pie"),
            optionspie
        );
        chartpie.render();

        // Chart pie
        $.ajax({
            datatype: 'json',
            url: "{{ url('/chart-data') }}",
            method: 'GET',
            data: {
                "mulai": "{{ date('Y-m-d') }}",
                "akhir": "{{ date('Y-m-d') }}"
            }
        }).done(async function(data1) {
            chartpie.updateOptions({
                noData: {
                    text: 'Loading...'
                },
                colors: ["#198754", "#0d6efd", "#6f42c1", "#FF9800", "#dc3545"],
                series: [
                data1[0].masuk,
                data1[0].libur,
                data1[0].izin_sakit,
                data1[0].izin_tidak_masuk,
                data1[0].alpha
                ],
                labels: [
                'Masuk',
                'Libur',
                'Sakit',
                'Izin Tidak Masuk',
                'Alpha'
                ]
            })
        });
    });
</script> --}}
@endpush
@endsection
