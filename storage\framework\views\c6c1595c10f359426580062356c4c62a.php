<?php $__env->startSection('isi'); ?>
<div class="row mb-2 mb-xl-3">
    <div class="col-auto">
        <h3><?php echo e($title); ?></h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <a href="<?php echo e(url('/siswa/tambah-siswa')); ?>" class="btn btn-primary">
            <i class="fa fa-plus"></i><span class="d-none d-md-inline"> Tambah</span>
        </a>

        <a href="<?php echo e(url('/siswa/export')); ?><?php echo e($_GET?'?'.$_SERVER['QUERY_STRING']: ''); ?>" class="btn btn-warning"><i
                class="fa fa-file-export"></i><span class="d-none d-md-inline"> Export</span></a>

        <button class="btn btn-success" type="button" data-bs-toggle="modal" data-original-title="test"
            data-bs-target="#exampleModal">
            <i class="fas fa-file-import"></i><span class="d-none d-md-inline"> Import</span>
        </button>

        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Import Users</h5>
                        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="<?php echo e(url('/siswa/import')); ?>" method="POST" enctype="multipart/form-data">
                        <div class="modal-body">
                            <?php echo csrf_field(); ?>
                            <div class="form-group">
                                <label for="file_excel">File Excel</label>
                                <input type="file" name="file_excel" id="file_excel"
                                    class="form-control <?php $__errorArgs = ['file_excel'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <?php $__errorArgs = ['file_excel'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback">
                                    <?php echo e($message); ?>

                                </div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Close</button>
                            <button class="btn btn-secondary" type="submit">Import Data</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12 col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="float-end">
                    <a href=" <?php echo e(url('/siswa')); ?>" class="btn btn-light">Semua</a>
                    <a href=" <?php echo e(url('/siswa?pembimbing=null')); ?>" class="btn btn-light">Belum
                        Mapping</a>
                    <a href="<?php echo e(url('/siswa?login=null')); ?>" class="btn btn-light">Belum
                        Login</a>
                </div>
                <form action="<?php echo e(url('/siswa')); ?>">
                    <div class="row mb-2">
                        <div class="col-lg-4 col-sm-12 col-md-6 col-xxl-4">
                            <input type="text" placeholder="Keyword ..." class="form-control"
                                value="<?php echo e(request('search')); ?>" name="search">
                        </div>
                        
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <button type="submit" id="search" class="btn btn-light">
                                <i class="fas fa-search"></i> Cari
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="table-responsive">
                <table class="table mb-0">
                    <thead>
                        <tr>
                            <th scope="col">No.</th>
                            <th scope="col">NIS</th>
                            <th scope="col">NISN</th>
                            <th scope="col">Nama</th>
                            <th scope="col">L/P</th>
                            <th scope="col">TTL</th>
                            <th scope="col">Kelas Awal</th>
                            <th scope="col" width="90px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $siswa; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ds): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td style="width: 50px;"><?php echo e(($siswa->currentPage() - 1) * $siswa->perPage() +
                                $loop->iteration); ?></td>
                            <td>
                                <?php echo e($ds->no_induk); ?>

                            </td>
                            <td><?php echo e($ds->nisn); ?></td>
                            <td><?php echo e($ds->nama ?? '-'); ?></td>
                            <td><?php echo e($ds->jenis_kelamin ?? '-'); ?></td>
                            <td><?php echo e($ds->tempat_lahir.', '.date('d-m-Y', strtotime($ds->tanggal_lahir)) ?? '-'); ?></td>
                            <td><?php echo e($ds->diterima_kelas ?? ''); ?></td>
                            <td class="text-nowrap">
                                <a class="btn btn-custom-xs btn-primary"
                                    href="<?php echo e(url('/siswa/detail/'.$ds->peserta_didik_id)); ?>" title="Edit"
                                    data-bs-toggle="tooltip" data-bs-placement="top">
                                    <i class="fa fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-search fa-2x mb-2"></i>
                                    <p>Tidak ada data siswa yang ditemukan</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php if($siswa->total() > 0): ?>
        <div class="d-flex justify-content-between align-items-center mt-3">
            <div class="text-muted">
                Menampilkan <?php echo e($siswa->firstItem()); ?> sampai <?php echo e($siswa->lastItem()); ?> dari <?php echo e($siswa->total()); ?> data
            </div>
            <div>
                <?php echo e($siswa->links()); ?>

            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('templates.dashboard', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\laragon\www\buku_induk_dev\resources\views/siswa/index.blade.php ENDPATH**/ ?>