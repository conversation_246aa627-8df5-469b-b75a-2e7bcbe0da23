<?php

namespace App\Http\Controllers;

use App\Models\TaModel;
use App\Models\SiswaModel;
use App\Models\SemesterModel;
use Illuminate\Http\Request;

class SiswaController extends Controller
{
    //
    public function index(Request $request)
    {
        $ta = TaModel::orderBy('tahun_ajaran_id', 'desc')->limit(3)->get();
        $smt = SemesterModel::orderBy('semester_id', 'desc')->get();

        // Start building the query
        $query = SiswaModel::query();

        // Handle search functionality
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('nama', 'LIKE', '%' . $search . '%')
                    ->orWhere('no_induk', 'LIKE', '%' . $search . '%')
                    ->orWhere('nisn', 'LIKE', '%' . $search . '%');
            });
        }

        // Handle filter parameters
        if ($request->get('pembimbing') === 'null') {
            // Filter for students without pembimbing
            // Note: Adjust field name based on actual database schema
            $query->whereNull('pembimbing_id');
        }

        if ($request->get('login') === 'null') {
            // Filter for students who haven't logged in
            // Note: Adjust field name based on actual database schema
            // Common field names: last_login, login_at, user_id (if null means no login)
            $query->whereNull('last_login');
        }

        // Apply ordering and pagination
        $siswa = $query->orderBy('peserta_didik_id', 'desc')
            ->paginate(20)
            ->withQueryString();

        return view('siswa.index', [
            'title' => 'Data Siswa',
            'ta' => $ta,
            'smt' => $smt,
            'siswa' => $siswa,
        ]);
    }
}
