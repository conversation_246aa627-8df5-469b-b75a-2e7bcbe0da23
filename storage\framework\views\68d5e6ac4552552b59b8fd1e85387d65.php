<?php if($paginator->hasPages()): ?>
<nav class="d-flex justify-content-center" aria-label="Page navigation">
    
    <div class="d-flex d-sm-none">
        <ul class="pagination pagination-sm mb-0">
            <?php if($paginator->onFirstPage()): ?>
            <li class="page-item disabled">
                <span class="page-link">Previous</span>
            </li>
            <?php else: ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>">Previous</a>
            </li>
            <?php endif; ?>

            <li class="page-item active">
                <span class="page-link"><?php echo e($paginator->currentPage()); ?></span>
            </li>

            <?php if($paginator->hasMorePages()): ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>">Next</a>
            </li>
            <?php else: ?>
            <li class="page-item disabled">
                <span class="page-link">Next</span>
            </li>
            <?php endif; ?>
        </ul>
    </div>

    
    <div class="d-none d-sm-flex">
        <ul class="pagination pagination-sm mb-0">
            
            <?php if($paginator->onFirstPage()): ?>
            <li class="page-item disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
            </li>
            <?php else: ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev"
                    aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
            <?php endif; ?>

            
            <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            
            <?php if(is_string($element)): ?>
            <li class="page-item disabled" aria-disabled="true"><span class="page-link"><?php echo e($element); ?></span></li>
            <?php endif; ?>

            
            <?php if(is_array($element)): ?>
            <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php if($page == $paginator->currentPage()): ?>
            <li class="page-item active" aria-current="page">
                <span class="page-link"><?php echo e($page); ?></span>
            </li>
            <?php else: ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
            </li>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            
            <?php if($paginator->hasMorePages()): ?>
            <li class="page-item">
                <a class="page-link" href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next"
                    aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
            <?php else: ?>
            <li class="page-item disabled" aria-disabled="true" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
            </li>
            <?php endif; ?>
        </ul>
    </div>
</nav>
<?php endif; ?>
<?php /**PATH D:\laragon\www\buku_induk_dev\resources\views/vendor/pagination/custom-bootstrap-5.blade.php ENDPATH**/ ?>