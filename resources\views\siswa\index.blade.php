@extends('templates.dashboard')
@section('isi')
<div class="row mb-2 mb-xl-3">
    <div class="col-auto">
        <h3>{{ $title }}</h3>
    </div>

    <div class="col-auto ms-auto text-end mt-n1">
        <a href="{{ url('/siswa/tambah-siswa') }}" class="btn btn-primary">
            <i class="fa fa-plus"></i><span class="d-none d-md-inline"> Tambah</span>
        </a>

        <a href="{{ url('/siswa/export') }}{{ $_GET?'?'.$_SERVER['QUERY_STRING']: '' }}" class="btn btn-warning"><i
                class="fa fa-file-export"></i><span class="d-none d-md-inline"> Export</span></a>

        <button class="btn btn-success" type="button" data-bs-toggle="modal" data-original-title="test"
            data-bs-target="#exampleModal">
            <i class="fas fa-file-import"></i><span class="d-none d-md-inline"> Import</span>
        </button>

        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Import Users</h5>
                        <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ url('/siswa/import') }}" method="POST" enctype="multipart/form-data">
                        <div class="modal-body">
                            @csrf
                            <div class="form-group">
                                <label for="file_excel">File Excel</label>
                                <input type="file" name="file_excel" id="file_excel"
                                    class="form-control @error('file_excel') is-invalid @enderror">
                                @error('file_excel')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                                @enderror
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Close</button>
                            <button class="btn btn-secondary" type="submit">Import Data</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-12 col-xl-12">
        <div class="card">
            <div class="card-header">
                <div class="float-end">
                    <a href=" {{url('/siswa')}}" class="btn btn-light">Semua</a>
                    <a href=" {{url('/siswa?pembimbing=null')}}" class="btn btn-light">Belum
                        Mapping</a>
                    <a href="{{url('/siswa?login=null')}}" class="btn btn-light">Belum
                        Login</a>
                </div>
                <form action="{{ url('/siswa') }}">
                    <div class="row mb-2">
                        <div class="col-lg-4 col-sm-12 col-md-6 col-xxl-4">
                            <input type="text" placeholder="Keyword ..." class="form-control"
                                value="{{ request('search') }}" name="search">
                        </div>
                        {{-- <div class="col-4">
                            <select name="lokasi_id" id="lokasi_id" class="form-control selectpicker"
                                data-live-search="true">
                                <option value="" selected>Lokasi PKL</option>
                                @foreach($data_lokasi as $j)
                                @if(request('lokasi_id') == $j->id)
                                <option value="{{ $j->id }}" selected>{{ $j->nama_lokasi }}</option>
                                @else
                                <option value="{{ $j->id }}">{{ $j->nama_lokasi }}</option>
                                @endif
                                @endforeach
                            </select>
                        </div> --}}
                        <div class="col-lg-2 col-sm-12 col-md-4 col-xxl-2 mb-2 mb-lg-0">
                            <button type="submit" id="search" class="btn btn-light">
                                <i class="fas fa-search"></i> Cari
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="table-responsive">
                <table class="table mb-0">
                    <thead>
                        <tr>
                            <th scope="col">No.</th>
                            <th scope="col">NIS</th>
                            <th scope="col">NISN</th>
                            <th scope="col">Nama</th>
                            <th scope="col">L/P</th>
                            <th scope="col">TTL</th>
                            <th scope="col">Kelas Awal</th>
                            <th scope="col" width="90px">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($siswa as $ds)
                        <tr>
                            <td style="width: 50px;">{{ ($siswa->currentPage() - 1) * $siswa->perPage() +
                                $loop->iteration }}</td>
                            <td>
                                {{ $ds->no_induk }}
                            </td>
                            <td>{{ $ds->nisn }}</td>
                            <td>{{ $ds->nama ?? '-' }}</td>
                            <td>{{ $ds->jenis_kelamin ?? '-' }}</td>
                            <td>{{ $ds->tempat_lahir.', '.date('d-m-Y', strtotime($ds->tanggal_lahir)) ?? '-' }}</td>
                            <td>{{ $ds->diterima_kelas ?? '' }}</td>
                            <td class="text-nowrap">
                                <a class="btn btn-custom-xs btn-primary"
                                    href="{{ url('/siswa/detail/'.$ds->peserta_didik_id) }}" title="Edit"
                                    data-bs-toggle="tooltip" data-bs-placement="top">
                                    <i class="fa fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <div class="text-muted">
                                    <i class="fas fa-search fa-3x mb-3 text-secondary"></i>
                                    <h5 class="text-muted">Tidak ada data siswa yang ditemukan</h5>
                                    @if(request('search'))
                                    <p class="mb-0">Coba ubah kata kunci pencarian Anda</p>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($siswa->total() > 0)
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col-sm-6 col-12 mb-2 mb-sm-0">
                    <div class="text-muted small text-center text-sm-start">
                        Menampilkan {{ $siswa->firstItem() }} sampai {{ $siswa->lastItem() }} dari {{ $siswa->total() }}
                        data
                    </div>
                </div>
                <div class="col-sm-6 col-12">
                    <div class="d-flex justify-content-center justify-content-sm-end">
                        {{ $siswa->links('pagination::custom-bootstrap-5') }}
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

@endsection
