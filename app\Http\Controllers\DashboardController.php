<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    //
    public function index()
    {
        // $users = DB::connection('mysql')->select('select * from users');
        // dd($users);

        return view('dashboard.index', [
            'title' => 'Dashboard',
            'ta' => $ta,
            'smt' => $smt,
        ]);
    }
}
