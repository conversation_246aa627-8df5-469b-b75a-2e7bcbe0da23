<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class SiswaPaginationTest extends TestCase
{
    /**
     * Test that the siswa index page loads successfully.
     */
    public function test_siswa_index_page_loads(): void
    {
        $response = $this->get('/siswa');

        $response->assertStatus(200);
        $response->assertViewIs('siswa.index');
        $response->assertViewHas(['title', 'siswa', 'ta', 'smt']);
    }

    /**
     * Test that search functionality works.
     */
    public function test_siswa_search_functionality(): void
    {
        $response = $this->get('/siswa?search=test');

        $response->assertStatus(200);
        $response->assertViewIs('siswa.index');
    }

    /**
     * Test that filter functionality works.
     */
    public function test_siswa_filter_functionality(): void
    {
        // Test pembimbing filter
        $response = $this->get('/siswa?pembimbing=null');
        $response->assertStatus(200);

        // Test login filter
        $response = $this->get('/siswa?login=null');
        $response->assertStatus(200);
    }

    /**
     * Test that pagination parameters are preserved.
     */
    public function test_pagination_preserves_query_parameters(): void
    {
        $response = $this->get('/siswa?search=test&page=2');

        $response->assertStatus(200);
        $response->assertViewIs('siswa.index');
    }
}
